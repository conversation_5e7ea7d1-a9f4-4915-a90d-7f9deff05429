import { Agency } from '../models/Agency';
export interface ParseOptions {
    minRating?: number;
}
export declare class HtmlParser {
    parseFile(filePath: string, options?: ParseOptions): Promise<Agency[]>;
    parseHtml(htmlContent: string, options?: ParseOptions): Agency[];
    private parseCard;
    private extractRating;
    private extractReviewCount;
    private extractLocation;
    private extractDescription;
    private extractPhone;
    private extractWebsite;
    private toFloat;
}
//# sourceMappingURL=HtmlParser.d.ts.map