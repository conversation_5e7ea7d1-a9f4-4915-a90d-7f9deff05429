"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileScanner = void 0;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
class FileScanner {
    constructor(options = {}) {
        this.options = {
            inputFolder: options.inputFolder || 'input',
            fileExtensions: options.fileExtensions || ['.html', '.htm'],
            recursive: options.recursive ?? false
        };
    }
    async scanForHtmlFiles() {
        const htmlFiles = [];
        try {
            await this.scanDirectory(this.options.inputFolder, htmlFiles, this.options.recursive);
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error(`Input folder '${this.options.inputFolder}' does not exist`);
            }
            throw error;
        }
        return htmlFiles.sort();
    }
    async scanDirectory(dirPath, htmlFiles, recursive) {
        const entries = await fs_1.promises.readdir(dirPath, { withFileTypes: true });
        for (const entry of entries) {
            const fullPath = path_1.default.join(dirPath, entry.name);
            if (entry.isDirectory() && recursive) {
                await this.scanDirectory(fullPath, htmlFiles, recursive);
            }
            else if (entry.isFile()) {
                const ext = path_1.default.extname(entry.name).toLowerCase();
                if (this.options.fileExtensions.includes(ext)) {
                    htmlFiles.push(fullPath);
                }
            }
        }
    }
    async validateInputFolder() {
        try {
            const stats = await fs_1.promises.stat(this.options.inputFolder);
            return stats.isDirectory();
        }
        catch {
            return false;
        }
    }
    async createInputFolder() {
        try {
            await fs_1.promises.mkdir(this.options.inputFolder, { recursive: true });
        }
        catch (error) {
            throw new Error(`Failed to create input folder '${this.options.inputFolder}': ${error.message}`);
        }
    }
    getInputFolder() {
        return this.options.inputFolder;
    }
    getSupportedExtensions() {
        return [...this.options.fileExtensions];
    }
}
exports.FileScanner = FileScanner;
//# sourceMappingURL=FileScanner.js.map