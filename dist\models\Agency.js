"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgencyBuilder = void 0;
class AgencyBuilder {
    constructor() {
        this.agency = {
            dataJson: {}
        };
    }
    setName(name) {
        this.agency.name = name;
        return this;
    }
    setRating(rating) {
        this.agency.rating = rating;
        return this;
    }
    setReviewCount(reviewCount) {
        this.agency.reviewCount = reviewCount;
        return this;
    }
    setLocation(location) {
        this.agency.location = location;
        return this;
    }
    setDescription(description) {
        this.agency.description = description;
        return this;
    }
    setContactPhone(contactPhone) {
        this.agency.contactPhone = contactPhone;
        return this;
    }
    setContactWebsite(contactWebsite) {
        this.agency.contactWebsite = contactWebsite;
        return this;
    }
    setClutchProfileUrl(clutchProfileUrl) {
        this.agency.clutchProfileUrl = clutchProfileUrl;
        return this;
    }
    setDataJson(dataJson) {
        this.agency.dataJson = dataJson;
        return this;
    }
    build() {
        return {
            name: this.agency.name ?? null,
            rating: this.agency.rating ?? null,
            reviewCount: this.agency.reviewCount ?? null,
            location: this.agency.location ?? null,
            description: this.agency.description ?? null,
            contactPhone: this.agency.contactPhone ?? null,
            contactWebsite: this.agency.contactWebsite ?? null,
            clutchProfileUrl: this.agency.clutchProfileUrl ?? null,
            dataJson: this.agency.dataJson ?? {}
        };
    }
}
exports.AgencyBuilder = AgencyBuilder;
//# sourceMappingURL=Agency.js.map