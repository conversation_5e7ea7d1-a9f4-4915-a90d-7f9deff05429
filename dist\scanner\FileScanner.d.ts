export interface ScanOptions {
    inputFolder: string;
    fileExtensions: string[];
    recursive: boolean;
}
export declare class FileScanner {
    private options;
    constructor(options?: Partial<ScanOptions>);
    scanForHtmlFiles(): Promise<string[]>;
    private scanDirectory;
    validateInputFolder(): Promise<boolean>;
    createInputFolder(): Promise<void>;
    getInputFolder(): string;
    getSupportedExtensions(): string[];
}
//# sourceMappingURL=FileScanner.d.ts.map