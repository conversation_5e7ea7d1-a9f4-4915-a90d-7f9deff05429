import { Agency } from '../models/Agency';
export declare const DEFAULT_DB_PATH = "clutchscrape.db";
export interface DatabaseStats {
    total: number;
    ratings: Record<string, number>;
    topLocations: Array<{
        location: string;
        count: number;
    }>;
}
export declare class Database {
    private db;
    private dbPath;
    constructor(dbPath?: string);
    private ensureSchema;
    upsertAgencies(agencies: Agency[]): Promise<number>;
    getStats(): Promise<DatabaseStats>;
    close(): void;
}
//# sourceMappingURL=Database.d.ts.map