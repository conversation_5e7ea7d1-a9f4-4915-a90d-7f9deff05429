# Clutch Scrape TypeScript

A TypeScript application for processing local HTML files to extract agency data from Clutch.co listings and store results in SQLite.

## Overview

This application has been refactored from the original Python web scraping application to work with local HTML files instead of making web requests. It maintains the same core functionality for data extraction and processing while using TypeScript and Node.js.

## Features

- **Local File Processing**: Processes HTML files from a local `input` folder instead of scraping live web pages
- **Data Extraction**: Extracts agency information including name, rating, review count, location, description, contact details
- **SQLite Storage**: Stores extracted data in a SQLite database with upsert functionality
- **Export Options**: Export data to JSON or CSV formats
- **Filtering**: Support for minimum rating filters
- **Statistics**: View statistics about processed data

## Installation

1. Install dependencies:
```bash
npm install
```

2. Build the TypeScript application:
```bash
npm run build
```

## Usage

### Processing HTML Files

Place your HTML files in the `input` folder and run:

```bash
# Process all HTML files in the input folder
npm start process

# Process with verbose logging
npm start process --verbose

# Process with minimum rating filter
npm start process --min-rating 4.5

# Process and export to JSON
npm start process --export output.json

# Process and export to CSV
npm start process --export output.csv

# Use custom input folder
npm start process --input-folder my-html-files
```

### View Statistics

```bash
npm start stats
```

### Command Line Options

#### Global Options
- `--db <path>`: SQLite database path (default: `clutchscrape.db`)
- `--verbose`: Enable verbose logging

#### Process Command Options
- `--input-folder <path>`: Input folder containing HTML files (default: `input`)
- `--min-rating <rating>`: Minimum rating filter (e.g., 4.5)
- `--export <path>`: Export path (.csv or .json)

## Project Structure

```
src/
├── cli/           # Command line interface
├── database/      # SQLite database functionality
├── models/        # Data models (Agency)
├── parser/        # HTML parsing and data extraction
├── scanner/       # File system scanning
└── main.ts        # Application entry point
```

## Development

### Scripts

- `npm run build`: Build TypeScript to JavaScript
- `npm run dev`: Run with ts-node for development
- `npm run clean`: Clean build directory
- `npm start`: Run the built application

### File Processing

The application scans the input folder for HTML files (`.html`, `.htm`) and processes each file to extract agency data. The same data extraction logic from the original Python application has been adapted to work with local files.

## Data Model

Each agency record contains:
- `name`: Agency name
- `rating`: Numerical rating (e.g., 4.8)
- `reviewCount`: Number of reviews
- `location`: Geographic location
- `description`: Agency description
- `contactPhone`: Phone number
- `contactWebsite`: Website URL
- `clutchProfileUrl`: Clutch.co profile URL
- `dataJson`: Additional metadata

## Database

The application uses SQLite to store extracted data with:
- Automatic schema creation
- Upsert functionality (insert or update based on profile URL)
- Indexes on rating and location for performance
- Timestamped records

## Migration from Python

This TypeScript version replaces the original Python application with the following changes:
- **Input Source**: Local HTML files instead of web scraping
- **Language**: TypeScript/Node.js instead of Python
- **Command**: `process` command instead of `scrape` command
- **Dependencies**: Cheerio for HTML parsing instead of BeautifulSoup

The core data extraction logic and database functionality remain equivalent to the original Python implementation.
