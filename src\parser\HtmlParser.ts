import * as cheerio from 'cheerio';
import { promises as fs } from 'fs';
import { Agency, AgencyBuilder } from '../models/Agency';

type CheerioElement = cheerio.Cheerio<any>;

export interface ParseOptions {
  minRating?: number;
}

export class HtmlParser {
  async parseFile(filePath: string, options: ParseOptions = {}): Promise<Agency[]> {
    try {
      const htmlContent = await fs.readFile(filePath, 'utf-8');
      return this.parseHtml(htmlContent, options);
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${(error as Error).message}`);
    }
  }

  parseHtml(htmlContent: string, options: ParseOptions = {}): Agency[] {
    const $ = cheerio.load(htmlContent);
    const agencies: Agency[] = [];

    // Try multiple selectors to be resilient to minor markup changes
    const listings = $('li.provider-row, div.provider-row, li.directory-listing, div.directory-listing');
    
    let elementsToProcess = listings;
    
    // Fallback: look for items with profile link
    if (listings.length === 0) {
      const profileLinks = $('a[href^="/profile/"]');
      const parents = profileLinks.map((_, el) => {
        const parent = $(el).closest('li, div');
        return parent.length > 0 ? parent[0] : null;
      }).get().filter(Boolean);
      elementsToProcess = $(parents as any);
    }

    elementsToProcess.each((_, card) => {
      try {
        const agency = this.parseCard($(card));
        
        // Apply minimum rating filter if specified
        if (options.minRating !== undefined && 
            (agency.rating === null || agency.rating < options.minRating)) {
          return; // Skip this agency
        }
        
        agencies.push(agency);
      } catch (error) {
        console.debug(`Failed to parse a listing card: ${(error as Error).message}`);
      }
    });

    return agencies;
  }

  private parseCard($card: CheerioElement): Agency {
    const builder = new AgencyBuilder();

    // Extract name and profile URL
    const nameEl = $card.find('a[href^="/profile/"]').first();
    if (nameEl.length === 0) {
      // Fallback to h3 a, h2 a
      const fallbackNameEl = $card.find('h3 a, h2 a').first();
      if (fallbackNameEl.length > 0) {
        builder.setName(fallbackNameEl.text().trim() || null);
        const href = fallbackNameEl.attr('href');
        if (href) {
          const profileUrl = href.startsWith('/') ? `https://clutch.co${href}` : href;
          builder.setClutchProfileUrl(profileUrl);
        }
      }
    } else {
      builder.setName(nameEl.text().trim() || null);
      const href = nameEl.attr('href');
      if (href) {
        const profileUrl = href.startsWith('/') ? `https://clutch.co${href}` : href;
        builder.setClutchProfileUrl(profileUrl);
      }
    }

    // Extract rating
    builder.setRating(this.extractRating($card));

    // Extract review count
    builder.setReviewCount(this.extractReviewCount($card));

    // Extract location
    builder.setLocation(this.extractLocation($card));

    // Extract description
    builder.setDescription(this.extractDescription($card));

    // Extract phone
    builder.setContactPhone(this.extractPhone($card));

    // Extract website
    builder.setContactWebsite(this.extractWebsite($card));

    // Set data JSON with raw HTML class
    const classes = $card.attr('class');
    builder.setDataJson({
      rawHtmlClass: classes ? classes.split(' ') : []
    });

    return builder.build();
  }

  private extractRating($card: CheerioElement): number | null {
    // Common patterns
    const selectors = [
      'span.rating, span.clutch-average, span[itemprop="ratingValue"]',
      'div.rating span',
      '[class*="rating"]'
    ];

    for (const selector of selectors) {
      const el = $card.find(selector).first();
      if (el.length > 0) {
        const value = this.toFloat(el.text());
        if (value !== null) {
          return value;
        }
      }
    }

    // Look for numbers like 4.8/5.0
    const text = $card.text();
    const ratingMatch = text.match(/(\d+(?:\.\d)?)\s*\/\s*5/);
    if (ratingMatch) {
      return this.toFloat(ratingMatch[1]);
    }

    // Look for standalone X.X near 'rating'
    const standaloneMatch = text.toLowerCase().match(/(\d+\.\d)(?=\s*(?:rating|stars))/);
    if (standaloneMatch) {
      return this.toFloat(standaloneMatch[1]);
    }

    return null;
  }

  private extractReviewCount($card: CheerioElement): number | null {
    const text = $card.text();
    const match = text.match(/(\d{1,5})\+?\s+review/i);
    return match ? parseInt(match[1]!, 10) : null;
  }

  private extractLocation($card: CheerioElement): string | null {
    const selectors = [
      'span.locality, span.location, .list-item__address, .location',
      '[class*="location"]'
    ];

    for (const selector of selectors) {
      const el = $card.find(selector).first();
      if (el.length > 0) {
        const text = el.text().trim();
        if (text) {
          return text;
        }
      }
    }

    return null;
  }

  private extractDescription($card: CheerioElement): string | null {
    const selectors = ['.module-list p', '.description', '.summary', 'p'];

    for (const selector of selectors) {
      const el = $card.find(selector).first();
      if (el.length > 0) {
        const text = el.text().trim();
        if (text && text.split(' ').length >= 3) {
          return text;
        }
      }
    }

    return null;
  }

  private extractPhone($card: CheerioElement): string | null {
    const text = $card.text();
    const match = text.match(/(\+?\d[\d\s().-]{6,}\d)/);
    return match ? match[1]! : null;
  }

  private extractWebsite($card: CheerioElement): string | null {
    const links = $card.find('a');
    
    for (let i = 0; i < links.length; i++) {
      const link = links.eq(i);
      const href = link.attr('href');
      const label = link.text().toLowerCase();
      
      if (href && href.startsWith('http') && 
          (label.includes('website') || label.includes('visit'))) {
        return href;
      }
    }

    return null;
  }

  private toFloat(s: string | undefined): number | null {
    if (!s) {
      return null;
    }
    
    try {
      const num = parseFloat(s.trim());
      return isNaN(num) ? null : num;
    } catch {
      return null;
    }
  }
}
