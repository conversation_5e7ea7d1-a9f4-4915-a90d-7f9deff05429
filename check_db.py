import sqlite3

conn = sqlite3.connect('clutchscrape.db')
conn.row_factory = sqlite3.Row

print("Database Schema:")
schema = conn.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='agencies'").fetchone()
if schema:
    print(schema[0])
else:
    print("No agencies table found")

print("\nTotal agencies:", conn.execute('SELECT COUNT(*) FROM agencies').fetchone()[0])

print("\nSample data:")
for row in conn.execute('SELECT name, rating, review_count, location FROM agencies ORDER BY rating DESC LIMIT 5').fetchall():
    print(f"  {row['name']} - {row['rating']}/5.0 ({row['review_count']} reviews) - {row['location']}")

conn.close()
