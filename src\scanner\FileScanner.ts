import { promises as fs } from 'fs';
import path from 'path';

export interface ScanOptions {
  inputFolder: string;
  fileExtensions: string[];
  recursive: boolean;
}

export class FileScanner {
  private options: ScanOptions;

  constructor(options: Partial<ScanOptions> = {}) {
    this.options = {
      inputFolder: options.inputFolder || 'input',
      fileExtensions: options.fileExtensions || ['.html', '.htm'],
      recursive: options.recursive ?? false
    };
  }

  async scanForHtmlFiles(): Promise<string[]> {
    const htmlFiles: string[] = [];
    
    try {
      await this.scanDirectory(this.options.inputFolder, htmlFiles, this.options.recursive);
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        throw new Error(`Input folder '${this.options.inputFolder}' does not exist`);
      }
      throw error;
    }

    return htmlFiles.sort(); // Sort for consistent processing order
  }

  private async scanDirectory(dirPath: string, htmlFiles: string[], recursive: boolean): Promise<void> {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory() && recursive) {
        await this.scanDirectory(fullPath, htmlFiles, recursive);
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name).toLowerCase();
        if (this.options.fileExtensions.includes(ext)) {
          htmlFiles.push(fullPath);
        }
      }
    }
  }

  async validateInputFolder(): Promise<boolean> {
    try {
      const stats = await fs.stat(this.options.inputFolder);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  async createInputFolder(): Promise<void> {
    try {
      await fs.mkdir(this.options.inputFolder, { recursive: true });
    } catch (error) {
      throw new Error(`Failed to create input folder '${this.options.inputFolder}': ${(error as Error).message}`);
    }
  }

  getInputFolder(): string {
    return this.options.inputFolder;
  }

  getSupportedExtensions(): string[] {
    return [...this.options.fileExtensions];
  }
}
