export interface Agency {
  name: string | null;
  rating: number | null;
  reviewCount: number | null;
  location: string | null;
  description: string | null;
  contactPhone: string | null;
  contactWebsite: string | null;
  clutchProfileUrl: string | null;
  dataJson: Record<string, any>;
}

export class AgencyBuilder {
  private agency: Partial<Agency> = {
    dataJson: {}
  };

  setName(name: string | null): this {
    this.agency.name = name;
    return this;
  }

  setRating(rating: number | null): this {
    this.agency.rating = rating;
    return this;
  }

  setReviewCount(reviewCount: number | null): this {
    this.agency.reviewCount = reviewCount;
    return this;
  }

  setLocation(location: string | null): this {
    this.agency.location = location;
    return this;
  }

  setDescription(description: string | null): this {
    this.agency.description = description;
    return this;
  }

  setContactPhone(contactPhone: string | null): this {
    this.agency.contactPhone = contactPhone;
    return this;
  }

  setContactWebsite(contactWebsite: string | null): this {
    this.agency.contactWebsite = contactWebsite;
    return this;
  }

  setClutchProfileUrl(clutchProfileUrl: string | null): this {
    this.agency.clutchProfileUrl = clutchProfileUrl;
    return this;
  }

  setDataJson(dataJson: Record<string, any>): this {
    this.agency.dataJson = dataJson;
    return this;
  }

  build(): Agency {
    return {
      name: this.agency.name ?? null,
      rating: this.agency.rating ?? null,
      reviewCount: this.agency.reviewCount ?? null,
      location: this.agency.location ?? null,
      description: this.agency.description ?? null,
      contactPhone: this.agency.contactPhone ?? null,
      contactWebsite: this.agency.contactWebsite ?? null,
      clutchProfileUrl: this.agency.clutchProfileUrl ?? null,
      dataJson: this.agency.dataJson ?? {}
    };
  }
}
