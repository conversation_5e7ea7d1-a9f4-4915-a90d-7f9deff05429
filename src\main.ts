#!/usr/bin/env node

import { ClutchScrapeCli } from './cli/cli';

async function main(): Promise<void> {
  const cli = new ClutchScrapeCli();
  
  try {
    await cli.run(process.argv);
  } catch (error) {
    console.error('Application error:', (error as Error).message);
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  main();
}

export { main };
