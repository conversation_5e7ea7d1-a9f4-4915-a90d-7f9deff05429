(()=>{"use strict";var t={355:(t,e,a)=>{a.d(e,{A:()=>s});const s='<div id="ai-assistant-chat-error-message" class="ai-assistant-chat__error-message"> <p class="ai-assistant-chat__error-message-text"> Something went wrong while generating the response. If this issue persists, please contact us through our help center at <a href="mailto:<EMAIL>" aria-label="Email <NAME_EMAIL>"><EMAIL></a>. </p> </div> '},359:(t,e,a)=>{a.d(e,{A:()=>s});const s='<div id="ai-assistant-chat-intro-message" class="ai-assistant-chat__intro-message"> <div class="ai-assistant-chat__intro-message-logo"></div> <h2 class="ai-assistant-chat__intro-message-title">Welcome to Clutch!</h2> <p class="ai-assistant-chat__intro-message-text"> We have over 350k service providers listed on our site, and more than 300k verified, in-depth project reviews and testimonials. </p> <p class="ai-assistant-chat__intro-message-text">Tell me about your project or ask me a question.</p> </div> '},525:(t,e,a)=>{a.d(e,{A:()=>s});const s='<div id="ai-assistant-sidebar-backdrop" class="ai-assistant-sidebar__backdrop ai-assistant-sidebar__backdrop--hidden"></div> <div id="ai-assistant-sidebar" class="ai-assistant-sidebar ai-assistant-sidebar--hidden"> <div class="ai-assistant-sidebar__header"> <div class="ai-assistant-sidebar__header-logotype"></div> <button type="button" class="ai-assistant-sidebar__header-close" id="ai-assistant-sidebar-close" aria-label="Close sidebar"></button> </div> <div id="ai-assistant-sidebar-loader" class="ai-assistant-sidebar__loader"> <div class="ai-assistant-sidebar__loader-spinner"></div> </div> <div id="ai-assistant-sidebar-chat" class="ai-assistant-sidebar__chat"></div> </div> <button type="button" aria-label="Open the chat" id="ai-assistant-sidebar-trigger" class="ai-assistant-sidebar__trigger"> <video loop muted autoplay playsinline src="https://bot.clutch.co/chatbot_static/video/purplesparkles.mp4"> </video> I need a recommendation... </button> '},563:(t,e,a)=>{a.d(e,{A:()=>s});const s='<div id="ai-assistant-chat-container" class="ai-assistant-chat__container"> <div id="ai-assistant-chat-messages" class="ai-assistant-chat__messages"></div> <div class="ai-assistant-chat__textarea-sticky-container"> <div id="ai-assistant-chat-textarea-wrap" class="ai-assistant-chat__textarea-wrap ai-assistant-chat__textarea-wrap--disabled"> <textarea id="ai-assistant-chat-textarea" class="ai-assistant-chat__textarea" placeholder="Say anything..."></textarea> <button id="ai-assistant-chat-submit-button" class="ai-assistant-chat__button-submit" aria-label="Submit Message"></button> </div> </div> </div> '}},e={};function a(s){var i=e[s];if(void 0!==i)return i.exports;var n=e[s]={exports:{}};return t[s](n,n.exports,a),n.exports}a.d=(t,e)=>{for(var s in e)a.o(e,s)&&!a.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);const s="ai-assistant-should-open";class i{constructor(t){t||console.error(`Event action is missing for ${this.action}, please add an action for object`),this.action=t,this.properties={}}setCategory(t){t?this.properties.event_category=t:console.error("Event category is required")}setLabel(t){t?this.properties.event_label=t:console.error("Event label is required")}setField(t,e){t&&null!=e?this.properties[t]=e:console.error(`.setField() method is failed. Please add name or values params: name - ${t}, value - ${e}`)}_collectGoogleAnalyticsKeys(){const t=[];return window.dataLayer?(window.dataLayer.forEach(e=>{"config"===e[0]&&e.length>=3&&!0===e[2].analyticjs&&!t.includes(e[1])&&t.push(e[1])}),t):t}sendGA(){const t=this.action,e=window.GlobalAnalyticsParameters?window.GlobalAnalyticsParameters.getParameters():{},a=this.properties;this._collectGoogleAnalyticsKeys().forEach(function(s){if(!s)return;const i={...e,...a};i.send_to=s;try{window.gtag("event",t,i)}catch(t){console.error(t)}})}sendHeap(){if(window.heap)try{const t=this.action,e=window.GlobalAnalyticsParameters?window.GlobalAnalyticsParameters.getParameters():{},a=this.properties;window.heap.track(t,{...e,...a})}catch(t){console.error(t)}}send(){this.sendGA(),window.heap&&this.sendHeap()}}class n{baseUrl="https://bot.clutch.co";setToken(t){localStorage.setItem("ai-chat-token",t)}getToken(){return localStorage.getItem("ai-chat-token")}async sendMessage(t,e){const a=`${this.baseUrl}/api/chat/${this.getToken()}`;try{const s=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:t})});if(s.ok)return await s.json();if(502===s.status)return;if(e>0)return await this.sendMessage(t,e-1)}catch(t){console.log(t)}}async createChat(){const t=`${this.baseUrl}/api/chat/`,e=await fetch(t,{method:"POST"});try{const t=(await e.json()).chat_token;this.setToken(t)}catch(t){console.log(t)}}async getChatHistory(){const t=`${this.baseUrl}/api/chat/${this.getToken()}`;try{const e=await fetch(t,{method:"GET"});return await e.json()}catch(t){console.log(t)}}}class r{api;id={sidebar:"ai-assistant-sidebar",sidebarChatContainer:"ai-assistant-sidebar-chat",chatContainer:"ai-assistant-chat-container",chatMessagesContainer:"ai-assistant-chat-messages",chatTextarea:"ai-assistant-chat-textarea",chatTextareaWrap:"ai-assistant-chat-textarea-wrap",chatSubmitButton:"ai-assistant-chat-submit-button",chatIntroMessage:"ai-assistant-chat-intro-message",chatErrorMessage:"ai-assistant-chat-error-message"};textareaHeight={min:27,max:140};constructor(){this.api=new n,this.init()}init(){this.initChatBaseTemplate(),this.initChat(),this.initEventListeners()}initChatBaseTemplate(){const t=a(563).A,e=document.getElementById(this.id.sidebarChatContainer);e&&e.insertAdjacentHTML("beforeend",t)}initEventListeners(){const t=document.getElementById(this.id.chatTextareaWrap),e=document.getElementById(this.id.chatTextarea),a=document.getElementById(this.id.chatSubmitButton),s=document.getElementById(this.id.chatMessagesContainer);t?.addEventListener("click",t=>this.handleTextareaWrapClick(t)),e?.addEventListener("input",t=>this.handleTextareaInput(t)),e?.addEventListener("keypress",t=>this.handleEnterKeyPress(t)),a?.addEventListener("click",()=>this.sendMessage()),s?.addEventListener("click",t=>this.handleChatMessagesContainerClick(t))}async initChat(){this.api.getToken()?await this.withLoader(this.loadChat()):await this.withLoader(this.createChat()),this.setFocusTextarea()}isTextareaValid(){const t=document.getElementById(this.id.chatTextareaWrap);if(!t)return!1;const e=t.classList.contains("ai-assistant-chat__textarea-wrap--disabled");return!(0===this.getTextareaValue().length||e)}handleEnterKeyPress(t){"Enter"===t.key&&(t.preventDefault(),this.sendMessage())}handleTextareaWrapClick(t){const e=t.target;Boolean(e.closest(`#${this.id.chatSubmitButton}`))||this.setFocusTextarea()}handleTextareaInput(t){const e=t.target;e.style.height=`${this.textareaHeight.min}px`,e.style.height=e.value.length?Math.min(e.scrollHeight,this.textareaHeight.max)+"px":`${this.textareaHeight.min}px`,this.setTextareaDisabled(e.value.length<=0)}handleChatMessagesContainerClick(t){const e=t.target?.closest(".ai-assistant-chat__message--assistant"),a=e?.querySelector("a");a&&(t.preventDefault(),sessionStorage.setItem(s,"true"),window.location.href=a.href)}getTextareaValue(){const t=document.getElementById(this.id.chatTextarea);return t?this.escapeHtml(t?.value):""}setFocusTextarea(){const t=document.getElementById(this.id.chatTextarea);t&&t.focus()}setTextareaDisabled(t){const e=document.getElementById(this.id.chatTextareaWrap);e&&e.classList.toggle("ai-assistant-chat__textarea-wrap--disabled",t)}scrollToLastResponse(){const t=document.getElementById(this.id.chatContainer);t&&requestAnimationFrame(()=>{t.scrollTo({top:t.scrollHeight,behavior:"smooth"})})}clearTextarea(){const t=document.getElementById(this.id.chatTextarea);t&&(t.value="",t.style.height=`${this.textareaHeight.min}px`,this.setTextareaDisabled(!0))}renderChatResponse(t){t&&("agent_response"===t.type?this.addMessage(t.text,!0):this.addErrorMessage())}addLoader(){const t=document.getElementById(this.id.chatMessagesContainer);if(!t)return;const e=document.createElement("div");e.className="ai-assistant-chat__loader--thinking",t.appendChild(e)}removeLoader(){const t=document.querySelector(".ai-assistant-chat__loader--thinking");t&&t.remove()}async sendMessage(){if(!this.isTextareaValid())return;const t=this.getTextareaValue();this.addMessage(t,!1),this.clearTextarea(),this.addLoader(),this.setTextareaDisabled(!0);const e=await this.api.sendMessage(t,4);e?(this.renderChatResponse(e),this.sentMessageEventTracking()):this.addErrorMessage(),this.removeLoader(),this.setTextareaDisabled(!1)}renderHistory(t){t.forEach(t=>{"user"===t.type?this.addMessage(t.text,!1):"assistant"===t.type&&this.addMessage(t.text,!0)})}escapeHtml(t){return t.replace(/[<>"'=`]/g,t=>({"<":"&lt;",">":"&gt;",'"':"&quot;","`":"&#x60;","=":"&#x3D;"}[t]||t))}createMessageElement(t,e){const a=document.createElement("div"),s="ai-assistant-chat__message "+(e?"ai-assistant-chat__message--assistant":"ai-assistant-chat__message--user");return a.className=s,a.innerHTML=t,a}addErrorMessage(){const t=document.getElementById(this.id.chatMessagesContainer);if(t){const e=a(355).A;t.insertAdjacentHTML("beforeend",e),this.scrollToLastResponse()}}addMessage(t,e){const a=document.getElementById(this.id.chatMessagesContainer);a&&(this.removeIntroMessage(),this.removeErrorMessage(),a.appendChild(this.createMessageElement(t,e)),this.scrollToLastResponse())}toggleChatLoading(t){const e=document.getElementById(this.id.sidebar);e&&e.classList.toggle("ai-assistant-sidebar--loading",t)}renderIntroMessage(){const t=document.getElementById(this.id.chatMessagesContainer);if(t){const e=a(359).A;t.insertAdjacentHTML("beforeend",e)}}removeIntroMessage(){const t=document.getElementById(this.id.chatIntroMessage);t&&t.remove()}removeErrorMessage(){const t=document.getElementById(this.id.chatErrorMessage);t&&t.remove()}async withLoader(t){this.toggleChatLoading(!0);try{return await t}finally{this.toggleChatLoading(!1)}}async createChat(){await this.api.createChat(),this.renderIntroMessage()}async loadChat(){const t=await this.api.getChatHistory();t?.history?.length?this.renderHistory(t.history):this.renderIntroMessage()}sentMessageEventTracking(){const t=new i("Chatbot - User Sent Message");t.setCategory("chatbot"),t.send()}}const o="ai-assistant-sidebar",c="ai-assistant-styles",d="ai-assistant-sidebar-trigger";function h(t){const e=document.getElementById(o),a=document.getElementById("ai-assistant-sidebar-backdrop"),s=document.getElementById(d);e&&e.classList.toggle("ai-assistant-sidebar--hidden",!t),a&&a.classList.toggle("ai-assistant-sidebar__backdrop--hidden",!t),s&&s.classList.toggle("ai-assistant-sidebar__trigger--hidden",t),window.innerWidth<=575&&(document.body.style.overflow=t?"hidden":""),document.dispatchEvent(new CustomEvent("TOGGLE_ASSISTANT_SIDEBAR",{detail:{open:t}})),window.assistantSidebarIsOpen=t,t?function(){const t=new i("Chatbot - Open Sidebar");t.setCategory("chatbot"),t.send()}():function(){const t=new i("Chatbot - Close Sidebar");t.setCategory("chatbot"),t.send()}()}async function l(){const t=document.getElementById("ai-assistant-chat-container");h(!0),t||new r}window.createAiAssistant=function(){if(!document.getElementById(c)){const t=document.createElement("link");t.id=c,t.rel="stylesheet",t.href="https://bot.clutch.co/chatbot_static/widget.css",t.onload=()=>{!function(){const t=a(525).A;document.body.insertAdjacentHTML("beforeend",t)}(),function(){const t=document.getElementById(o),e=document.getElementById("ai-assistant-sidebar-close"),a=document.getElementById(d);t&&document.addEventListener("mousedown",function(e){t.classList.contains("ai-assistant-sidebar--hidden")||t.contains(e.target)||window.innerWidth<768&&h(!1)}),e&&e.addEventListener("click",()=>h(!1)),a&&a.addEventListener("click",l)}(),"true"===sessionStorage.getItem(s)&&(sessionStorage.removeItem(s),l())},document.head.appendChild(t)}}})();