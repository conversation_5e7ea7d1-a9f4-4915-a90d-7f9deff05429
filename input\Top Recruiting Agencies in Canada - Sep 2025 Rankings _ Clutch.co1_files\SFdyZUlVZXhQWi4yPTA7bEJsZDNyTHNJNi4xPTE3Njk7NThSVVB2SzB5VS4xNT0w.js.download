(function() {
function isDefined(t){return void 0!==t}function getFromLocalStorage(){var t=localStorage.getItem("optux")||"{}";try{return JSON.parse(t)}catch(t){return{}}}function setInLocalStorage(t,e){optuxLocalStore[t]=e,localStorage.optux=JSON.stringify(optuxLocalStore)}function isExperimentActive(t){var e=RUNNING_EXPERIMENTS.get(t);return!!e&&e.active}function parseInlineData(t){for(var e={},r=0,o=t.split(";");r<o.length;r++){var a=o[r];if(a){var n=a.match(/^([^.]+).(d+)=(d+)(?:[(.*)])?$/);if(n){var i=n[1],c=n[2],N=n[3],p=n[4];e[i]={ver:parseInt(c,10),variant:parseInt(N,10)},n[0].includes("[")&&(e[i].ideas=p?p.split(",").sort():[])}}}return e}var ON_EXPERIMENT_ENTER="optuxexperimententer",ON_EXPERIMENT_LEAVE="optuxexperimentleave",ON_INIT="optuxinit",optuxLocalStore=getFromLocalStorage(),RUNNING_EXPERIMENTS=new Map([["aQSjCAQOcg",{"urlPattern":"^\\/get-listed$","variant":0,"active":false,"triggerCount":0}],["8QWpBTUraG",{"urlPattern":"^\\/clutch-verified$","variant":0,"active":false,"triggerCount":0}],["btREaN8Ed6",{"urlPattern":"^\\/get-matched\\/seo$","variant":0,"active":false,"triggerCount":0}],["lBld3rLsI6",{"urlPattern":"^\\/.*$","variant":1769,"active":false,"triggerCount":0}],["JfRU4FlXPD",{"urlPattern":"^\\/get-matched\\/web-development$","variant":0,"active":false,"triggerCount":0}]]),CONVERSION_GOALS=[{"g":301,"e":"aQSjCAQOcg","v":0,"ev":"click","s":"#iehdv","u":"^\\/get-listed$"},{"g":296,"e":"8QWpBTUraG","v":0,"ev":"click","s":"#red-verify-business-btn-top, #red-verify-business-btn-top-2, #red-verify-business-btn-top-3","u":"^\\/clutch-verified$"},{"g":510,"e":"btREaN8Ed6","v":0,"ev":"click","s":"#ihpfft","u":"^\\/get-matched\\/seo$"},{"g":524,"e":"lBld3rLsI6","v":1769,"ev":"click","s":"#cta-banner-submit-button","u":"^\\/.*$"},{"g":509,"e":"JfRU4FlXPD","v":0,"ev":"click","s":"#ihpfft","u":"^\\/get-matched\\/web-development$"}],ANALYTICS_BASE_URL="/_optux/analytics/v1/cpacmhfgbw/OUX-E0A6E10C-3B51-416F-B824-386F4EA18F9D";var beacon_supported=window.navigator&&"function"==typeof window.navigator.sendBeacon&&"function"==typeof window.Blob,_dataQueue=[],timer=null;function send(e,n,i,o,t){var a,d;void 0===o&&(o=!1);var u={event:e,experiment:n,variant:"object"==typeof i&&null!==i&&"variant"in i?i.variant:i,pageLocation:null===(a=null===window||void 0===window?void 0:window.location)||void 0===a?void 0:a.href,personalization_uuids:(null===(d=window.optux)||void 0===d?void 0:d.personalization_uuids)||[]};"object"==typeof i&&null!==i&&i.ideas&&(u.personalization_uuids=(u.personalization_uuids||[]).concat(i.ideas)),isDefined(t)&&Object.assign(u,t),_dataQueue.push(u),o||_dataQueue.length>=5?(clearTimeout(timer),timer=null,_send()):timer||(timer=setTimeout(_send,1e3))}function _send(){var e=_dataQueue.splice(0,_dataQueue.length);if(0===e.length);else{var n=ANALYTICS_BASE_URL+"/batch",i=JSON.stringify(e),o=!1;if(beacon_supported){var t=new window.Blob([i],{type:"application/json"});try{o=window.navigator.sendBeacon(n,t)}catch(e){}}o||fetch(n+"?t="+"xhr",{method:"POST",headers:{"Content-Type":"application/json"},body:i,cache:"no-cache",credentials:"omit"})}}var STORAGE_KEY="optuxUserInfo",RESEND_INTERVAL=18e5;function getUTMs(){var e=window.optux;if(!isDefined(e)||!isDefined(e.data)||!isDefined(e.data.utms))return{};for(var t=e.data.utms,n=["source","medium","campaign","content","term"],i={},o=0,r=Object.entries(t);o<r.length;o++){var a=r[o],s=a[0],f=a[1];if(f){var E=s.startsWith("utm_")?s.slice(4):s;n.includes(E)&&(i[E]=f)}}return i}function sendUserInformation(){var e=sessionStorage.getItem(STORAGE_KEY)||"";if(e){var t=new Date(e).getTime();if((new Date).getTime()-t<RESEND_INTERVAL)return}var n=JSON.stringify({utm:getUTMs()});fetch(ANALYTICS_BASE_URL+"/info",{method:"POST",headers:{"Content-Type":"application/json"},body:n}).then((function(e){e.ok&&sessionStorage.setItem(STORAGE_KEY,(new Date).toISOString())})).catch((function(){}))}document.addEventListener(ON_EXPERIMENT_ENTER,(function(){sendUserInformation()}),{once:!0});var ENGAGEMENT_EVENTS=["click","keypress"];function engagementListener(e,n,t){isExperimentActive(n)&&send(e,n,t)}document.addEventListener(ON_EXPERIMENT_ENTER,(function(e){var n=e.detail.slug,t=e.detail.variant;send("pageview",n,t);for(var E=0;E<ENGAGEMENT_EVENTS.length;E++){var i=ENGAGEMENT_EVENTS[E],a=engagementListener.bind(null,i,n,t);document.removeEventListener(i,a),document.addEventListener(i,a,{once:!0,passive:!0})}}));function parseExperimentsData(r){var a={};if(!r)return a;for(var e=0,i=r.split(";");e<i.length;e++){var n=i[e].split("="),t=n[0],s=n[1];if(t&&s){var v=s.indexOf("["),p=t.indexOf("."),f=p>0?t.substring(0,p):t;if(v>0){var d=parseInt(s.substring(0,v))||0,u=s.substring(v+1,s.length-1),l=u?u.split(","):[];a[f]={variant:d,ideas:l}}else a[f]={variant:parseInt(s)||0}}}return a}!function(){var r=window.optux;if(!isDefined(r))return{};var a=r.data;if(!a||!a.experiments)return{};var e=parseExperimentsData(a.experiments);for(var i in e){var n=e[i];n.ideas&&0!==n.ideas.length&&send("personalization",i,{variant:n.variant,ideas:n.ideas},!1)}}();function executedAlready(e,n){var t=e+"_"+n;return!!optuxLocalStore[t]||(setInLocalStorage(t,!0),!1)}function sendData(e,n,t){var o;void 0===t&&(t=!1);var a=function(a){return n.hasOwnProperty(a)?t&&executedAlready(e,a)?"continue":void((null===(o=RUNNING_EXPERIMENTS.get(a))||void 0===o?void 0:o.active)?n[a]():document.addEventListener(ON_EXPERIMENT_ENTER,(function(){n[a]()}),{once:t})):"continue"};for(var i in n)a(i)}function waitForIntegration(e,n,t){if(isDefined(window[e]))sendData(e,n,t);else var o=setInterval((function(){isDefined(window[e])&&(clearInterval(o),sendData(e,n,t))}),1e3)}waitForIntegration("gtag", {"lBld3rLsI6": () => gtag("event", "experience_impression", {"exp_variant_string": "OPTUX-lBld3rLsI6-1769"}),}, true);waitForIntegration("heap", {"lBld3rLsI6": () => heap.addUserProperties({"experiment_lBld3rLsI6": "Original"}),}, true);function listen(i){window.addEventListener(i.ev,(function(s){var t=s.target;t&&(t.matches(i.s)||t.closest(i.s))&&isExperimentActive(i.e)&&send("conversion",i.e,i.v,!0,{goal:i.g})}),{capture:!0,passive:!0})}for(var clicksAndSubmits=[{"g":301,"e":"aQSjCAQOcg","v":0,"ev":"click","s":"#iehdv","u":"^\\/get-listed$"},{"g":296,"e":"8QWpBTUraG","v":0,"ev":"click","s":"#red-verify-business-btn-top, #red-verify-business-btn-top-2, #red-verify-business-btn-top-3","u":"^\\/clutch-verified$"},{"g":510,"e":"btREaN8Ed6","v":0,"ev":"click","s":"#ihpfft","u":"^\\/get-matched\\/seo$"},{"g":524,"e":"lBld3rLsI6","v":1769,"ev":"click","s":"#cta-banner-submit-button","u":"^\\/.*$"},{"g":509,"e":"JfRU4FlXPD","v":0,"ev":"click","s":"#ihpfft","u":"^\\/get-matched\\/web-development$"}],_i=0,clicksAndSubmits_1=clicksAndSubmits;_i<clicksAndSubmits_1.length;_i++){var goal=clicksAndSubmits_1[_i];listen(goal)}for(var _i=0,_a=Array.from(RUNNING_EXPERIMENTS.entries());_i<_a.length;_i++){var _b=_a[_i],slug=_b[0],details=_b[1],url_match=null!==window.location.pathname.match(details.urlPattern);url_match&&(details.active=!0,document.dispatchEvent(new CustomEvent(ON_EXPERIMENT_ENTER,{detail:{slug:slug,variant:details.variant,triggerCount:0}})),document.dispatchEvent(new CustomEvent(ON_INIT,{detail:{path:window.location.pathname}})))}
})();