!function(){"use strict";var t={p:""};t.p,function(){var t=function(t,e){let i;return function(...n){clearTimeout(i),i=setTimeout((()=>t.apply(this,n)),e)}};const e=100,i="sg-input-field-v2--failed-validation",n={REQUIRED:"Tell us about your project goals.",TOO_LONG:"Please shorten your response to 100 characters or less."};class s{banner;closeButton;inputField;formEl;fieldEl;validationTextEl;SESSION_STORAGE_KEY="ctaBannerClosed";DIRECTORY_CARD_SELECTOR=".provider-list-item";isVisible=!1;hasValidationEnabled=!1;constructor(){this.banner=document.querySelector(".cta-banner"),this.closeButton=document.querySelector(".cta-banner__close"),this.inputField=document.querySelector("#ctaBanner-input"),this.formEl=document.getElementById("pb-search-form"),this.fieldEl=document.getElementById("ctaBanner"),this.validationTextEl=null;const t=document.getElementById("providers2");this.hasValidationEnabled="input-banner"===t?.getAttribute("data-cta-variant"),this.init()}init(){this.banner&&(this.wasBannerClosedThisSession()||(this.setupEventListeners(),this.setupScrollListener(),this.setupFormSubmission(),this.hasValidationEnabled&&this.setupValidation(),this.hideBanner()))}setupEventListeners(){this.closeButton&&this.closeButton.addEventListener("click",(t=>{t.preventDefault(),t.stopPropagation(),this.closeBanner()}))}setupFormSubmission(){this.formEl&&this.formEl.addEventListener("submit",this.handleSubmit.bind(this))}setupValidation(){this.formEl&&this.inputField&&this.fieldEl&&(this.validationTextEl||(this.validationTextEl=document.createElement("div"),this.validationTextEl.className="sg-input-field-v2__validation-text",this.validationTextEl.textContent=n.REQUIRED,this.fieldEl.appendChild(this.validationTextEl)),this.inputField.addEventListener("input",this.handleInput.bind(this)),this.inputField.setAttribute("maxlength",e.toString()))}resetValidationError(){this.fieldEl&&this.validationTextEl&&(this.fieldEl.classList.remove(i),this.validationTextEl.textContent=n.REQUIRED)}showValidationError(t){this.fieldEl&&this.validationTextEl&&(this.validationTextEl.textContent=t,this.fieldEl.classList.add(i))}handleInput=t((t=>{if(!this.hasValidationEnabled)return;const i=t.target.value;this.resetValidationError(),i.length>e&&this.showValidationError(n.TOO_LONG)}),150);handleSubmit=t=>{if(!this.inputField||!this.formEl)return;t.preventDefault();const i=this.inputField.value.trim();if(this.hasValidationEnabled){if(!i)return void this.showValidationError(n.REQUIRED);if(i.length>e)return void this.showValidationError(n.TOO_LONG)}try{const t=this.formEl.dataset.projectBriefUrl;if(!t)return void console.error("Missing project brief URL");const e=new URL(t,window.location.origin);e.searchParams.set("business_goal",i||" "),location.href=e.toString()}catch(t){console.error("Failed to redirect:",t)}};setupScrollListener(){const t=document.querySelectorAll(this.DIRECTORY_CARD_SELECTOR);if(t.length<4)return;const e=t[3];if(!e)return;const i=new IntersectionObserver((t=>{t.forEach((t=>{if(!this.isVisible){const e=t.boundingClientRect;(t.isIntersecting||e.bottom<0)&&(this.showBanner(),i.disconnect())}}))}),{root:null,rootMargin:"0px",threshold:0});i.observe(e),setTimeout((()=>{if(!this.isVisible){const t=e.getBoundingClientRect();(t.bottom<0||t.top<window.innerHeight&&t.bottom>0)&&(this.showBanner(),i.disconnect())}}),100)}showBanner(){this.banner&&!this.isVisible&&(this.wasBannerClosedThisSession()||(this.banner.style.display="flex",this.isVisible=!0,this.banner.offsetHeight,setTimeout((()=>{this.banner.classList.add("cta-banner--visible")}),10)))}hideBanner(){this.banner&&(this.banner.style.display="none",this.banner.classList.remove("cta-banner--visible"),this.isVisible=!1)}closeBanner(){this.banner&&(sessionStorage.setItem(this.SESSION_STORAGE_KEY,"true"),this.banner.classList.remove("cta-banner--visible"),setTimeout((()=>{this.hideBanner()}),300),this.isVisible=!1)}wasBannerClosedThisSession(){return"true"===sessionStorage.getItem(this.SESSION_STORAGE_KEY)}}var o;o=()=>new s,"loading"===document.readyState?document.addEventListener("DOMContentLoaded",o):o()}()}();