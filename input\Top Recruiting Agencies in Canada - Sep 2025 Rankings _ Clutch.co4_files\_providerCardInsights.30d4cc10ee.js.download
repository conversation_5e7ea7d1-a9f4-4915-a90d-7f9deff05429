!function(){"use strict";var e={8786:function(e,t,s){s.d(t,{J:function(){return i}});class i{constructor(e){e||console.error(`Event action is missing for ${this.action}, please add an action for object`),this.action=e,this.properties={}}setCategory(e){e?this.properties.event_category=e:console.error("Event category is required")}setLabel(e){e?this.properties.event_label=e:console.error("Event label is required")}setField(e,t){e&&null!=t?this.properties[e]=t:console.error(`.setField() method is failed. Please add name or values params: name - ${e}, value - ${t}`)}_collectGoogleAnalyticsKeys(){const e=[];return window.dataLayer?(window.dataLayer.forEach((t=>{"config"===t[0]&&t.length>=3&&!0===t[2].analyticjs&&!e.includes(t[1])&&e.push(t[1])})),e):e}sendGA(){const e=this.action,t=window.GlobalAnalyticsParameters?window.GlobalAnalyticsParameters.getParameters():{},s=this.properties;this._collectGoogleAnalyticsKeys().forEach((function(i){if(!i)return;const n={...t,...s};n.send_to=i;try{window.gtag("event",e,n)}catch(e){console.error(e)}}))}sendHeap(){if(window.heap)try{const e=this.action,t=window.GlobalAnalyticsParameters?window.GlobalAnalyticsParameters.getParameters():{},s=this.properties;window.heap.track(e,{...t,...s})}catch(e){console.error(e)}}send(){this.sendGA(),window.heap&&this.sendHeap()}}},7259:function(e,t,s){s.d(t,{Fg:function(){return r}});var i=s(8786);!function(){let e=!1}();function n(e,t="dom_"){if(!e)return;let s=r(e,t);switch(e.localName){case"a":e.innerText?s[`${t}_label`]=e.innerText.trim():e.title?s[`${t}_label`]=e.title.trim():s[`${t}_label`]=e.getAttribute("aria-label");break;case"button":case"label":e.innerText?s[`${t}_label`]=e.innerText.trim():e.value?s[`${t}_label`]=e.value.trim():s[`${t}_label`]=e.getAttribute("aria-label");break;case"select":let i=e.id;s[`${t}_label`]=document.querySelector(`[for='${i}']`).innerText.trim()}return s}function r(e,t="dom_"){if(!e)return;const s=Object.assign({},e.dataset),i={};return Object.keys(s).forEach((e=>{e.includes("gtm_")?i[e.replace("gtm_","")]=s[e]:i[`${t}_data-${e}`]=s[e]})),{...i,...e.classList.value&&{[`${t}_class_name`]:e.classList.value},...e.id&&{[`${t}_id`]:e.id},...e.parentElement&&e.parentElement.classList&&e.parentElement.classList.value&&{[`${t}_parent_class_name`]:e.parentElement.classList.value.trim()},...e.parentElement&&e.parentElement.id&&{[`${t}_parent_id`]:"string"==typeof e.parentElement.id?e.parentElement.id.trim():e.parentElement.id}}}}},t={};function s(i){var n=t[i];if(void 0!==n)return n.exports;var r=t[i]={exports:{}};return e[i](r,r.exports,s),r.exports}s.d=function(e,t){for(var i in t)s.o(t,i)&&!s.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var i=["POST","PUT","DELETE","PATCH"];var n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=e;if(i.includes(t.method)){var n=function(e){for(var t=e+"=",s=decodeURIComponent(document.cookie).split(";"),i=0;i<s.length;i++){for(var n=s[i];" "===n.charAt(0);)n=n.substring(1);if(0===n.indexOf(t))return n.substring(t.length,n.length)}return null}("__csrftoken");n&&(t.headers={"X-CSRF-Token":n})}return fetch(s,t)};const r="sg-token-v2--selected",a="sg-tabs-expanded__section--industries",o="sg-tabs-expanded__section--preloader",l="sg-tabs-expanded__section--industries-general",c="sg-tabs-expanded__section--industries-selected";function d(e){e.querySelectorAll(`.${r}`).forEach((e=>e.classList.remove(r)))}function h(e){e.querySelector(".provider-insight__features")?.remove(),e.querySelector(".provider-insight__scrollbar-container")?.remove()}function u(e){h(e),d(e),e.classList.remove(c),e.classList.add(l)}function m(e,t){const s=t.dataset.insightId,i=t.dataset.providerSlug;s&&i&&!e.classList.contains(o)&&(!function(e){e.classList.add(o)}(e),n(`/industry-insight/${i}/${s}`).then((e=>{if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);return e.text()})).then((s=>{!function(e,t,s){h(e),d(e),t.classList.add(r),e.classList.remove(l),e.classList.add(c),e.querySelector(".provider-insight__industries")?.insertAdjacentHTML("afterend",s)}(e,t,s)})).catch((e=>{console.error("Failed to fetch industry insight:",e)})).finally((()=>{!function(e){e.classList.remove(o)}(e)})))}const b="sg-tabs-expanded";class p{constructor(e){this.parentElementClassName=e.parentElementClassName,this.clickTabCallback=e.clickTabCallback,this.withPreloader=e.withPreloader,this.init()}parentNodeElement=null;tabsExpandedElements=null;activeTabsExpandedElement=null;init(){this.defineElements(),this.addOnClickTabsListeners()}defineElements(){this.parentNodeElement=document.querySelector(`.${this.parentElementClassName}`),this.tabsExpandedElements=this.parentNodeElement?.querySelectorAll(".sg-tabs-expanded")}addOnClickTabsListeners(){this.tabsExpandedElements&&this.tabsExpandedElements.forEach((e=>{const t=e.querySelectorAll(".sg-tabs-expanded__tab:not(.sg-tabs-expanded__tab--empty)");t.forEach((e=>e.addEventListener("click",(e=>this.onClickListenerHandler(e,t)))))}))}onClickListenerHandler=(e,t)=>{const s=e.target;s&&(this.activateTabHandler(s,t),this.clickTabCallback&&this.clickTabCallback(e))};activateTabHandler(e,t){this.activeTabsExpandedElement=e.closest(`.${b}`),e.classList.contains(`${b}__tab--active`)?e.classList.remove(`${b}__tab--active`):(this.withPreloader&&this.showPreloader(),t.forEach((e=>{e.classList.remove(`${b}__tab--active`)})),e.classList.add(`${b}__tab--active`)),this.activateTabSectionContentHandler(e,t)}activateTabSectionContentHandler(e,t){const s=Array.from(t).indexOf(e);this.activeTabsExpandedElement?.querySelectorAll(`.${b}__section`).forEach(((e,t)=>{t===s?e.classList.toggle(`${b}__section--active`):e.classList.remove(`${b}__section--active`)}))}showPreloader(){this.activeTabsExpandedElement?.querySelector(`.${b}__sections-wrapper`)?.classList.add(`${b}__sections-wrapper--preloader`)}hidePreloader(){this.activeTabsExpandedElement?.querySelector(`.${b}__sections-wrapper`)?.classList.remove(`${b}__sections-wrapper--preloader`)}}class v{container;buttonsContainer;button;message;removeAfterClick;constructor(e,t=!1){const s=document.querySelector(e);if(!s)return void console.warn(`Feedback container not found: ${e}`);this.container=s,this.removeAfterClick=t;const i=this.container.querySelector(".feedback__buttons"),n=this.container?.querySelectorAll(".feedback__button"),r=this.container.querySelector(".feedback__message");n&&r?(this.buttonsContainer=i,this.button=n,this.message=r,this.button?.forEach((e=>e.addEventListener("click",(()=>this.handleClick()))))):console.warn(`Feedback sub-elements missing in ${e}`)}handleClick(){this.showThankYou(),this.hideButtons(),setTimeout((()=>this.hideSection()),3e4)}showThankYou(){this.message.textContent="Thanks for your feedback!"}hideButtons(){this.buttonsContainer?this.buttonsContainer.style.display="none":this.button.forEach((e=>e.style.display="none"))}hideSection(){this.removeAfterClick?this.container.remove():this.container.style.display="none"}}var f,_=s(8786),g=s(7259);class E{constructor(e,t,s,i="",n=""){this.parentSelector=n,this.selector=e,this.eventName=s,this.parameters=t,this.targetElements=[],this.visibilityTimers=new Map,this.mutableContainerElement=i?document.querySelector(i):void 0,this.delay=0}start(e,t=1){this.delay=e,this.targetElements=this.parentSelector?document.querySelector(this.parentSelector).querySelectorAll(this.selector):document.querySelectorAll(this.selector),this.intersectionObserver=new IntersectionObserver((e=>{e.forEach((e=>{e.isIntersecting?this.startVisibilityTimer(e.target):this.clearVisibilityTimer(e.target)}))}),{threshold:t}),this.targetElements.forEach((e=>this.intersectionObserver.observe(e))),this.mutableContainerElement&&(this.mutationObserver=new MutationObserver((e=>{for(const t of e)for(const e of t.addedNodes)if(e.classList&&this.selector.split(" ").some((t=>e.classList.contains(t.substring(1))))&&(this.intersectionObserver.observe(e),this.targetElements=[...this.targetElements,e]),e.hasChildNodes()){const t=e.querySelector(this.selector);t&&(this.intersectionObserver.observe(t),this.targetElements=[...this.targetElements,t])}})),this.mutationObserver.observe(this.mutableContainerElement,{childList:!0,subtree:!1,attributes:!1,characterData:!1}))}startVisibilityTimer(e){if(this.visibilityTimers.has(e))return;const t=setTimeout((()=>{this.sendEvent(e),this.intersectionObserver.unobserve(e),this.visibilityTimers.delete(e)}),this.delay);this.visibilityTimers.set(e,t)}clearVisibilityTimer(e){const t=this.visibilityTimers.get(e);t&&(clearTimeout(t),this.visibilityTimers.delete(e))}sendEvent(e){const t={...(0,g.Fg)(e),...this.parameters};let s=new _.J(this.eventName);Object.keys(t).forEach((e=>{s.setField(e,t[e])})),s.send()}}function y(){new p({parentElementClassName:"providers__list",clickTabCallback:e=>{const t=e.target;if(t&&!t.dataset.feedback){if("reviews-mention"===t.dataset.insight){new E(".provider-insight__review",{},"Directory Insight Review Impression","",`#provider-${t.dataset.gtm_provider_id}`).start(1e3,1)}t.setAttribute("data-feedback","true"),new v(`#insight-${t.dataset.gtm_insight_id}-feedback`,!0)}}});new E(".sg-tabs-expanded__tab",{},"Directory Insight Impression").start(3e3,.2),function(){const e=document.getElementById("providers__list");e?e.addEventListener("click",(e=>{const t=e.target;if(t.classList.contains("provider-insight__industry-token")){const e=t.closest(`.${a}`);if(!e)return;t.classList.contains(r)?u(e):m(e,t)}else if(t.classList.contains("provider-insight__back-button")){const e=t.closest(`.${a}`);e&&u(e)}})):console.warn("Element #providers__list not found")}()}f=function(){y(),window.updateProviderCardInsights=y},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",f):f()}();