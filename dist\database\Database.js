"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Database = exports.DEFAULT_DB_PATH = void 0;
const sqlite3_1 = __importDefault(require("sqlite3"));
exports.DEFAULT_DB_PATH = 'clutchscrape.db';
class Database {
    constructor(dbPath = exports.DEFAULT_DB_PATH) {
        this.dbPath = dbPath;
        this.db = new sqlite3_1.default.Database(dbPath);
        this.ensureSchema();
    }
    async ensureSchema() {
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                this.db.run(`
          CREATE TABLE IF NOT EXISTS agencies (
            id INTEGER PRIMARY KEY,
            name TEXT,
            rating REAL,
            review_count INTEGER,
            location TEXT,
            description TEXT,
            contact_phone TEXT,
            contact_website TEXT,
            clutch_profile_url TEXT UNIQUE,
            data_json TEXT,
            scraped_at TEXT
          )
        `);
                this.db.run(`
          CREATE INDEX IF NOT EXISTS idx_agencies_rating ON agencies(rating)
        `);
                this.db.run(`
          CREATE INDEX IF NOT EXISTS idx_agencies_location ON agencies(location)
        `, (err) => {
                    if (err) {
                        reject(err);
                    }
                    else {
                        resolve();
                    }
                });
            });
        });
    }
    async upsertAgencies(agencies) {
        return new Promise((resolve, reject) => {
            let count = 0;
            const stmt = this.db.prepare(`
        INSERT INTO agencies(
          name, rating, review_count, location, description,
          contact_phone, contact_website, clutch_profile_url,
          data_json, scraped_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON CONFLICT(clutch_profile_url) DO UPDATE SET
          name=excluded.name,
          rating=excluded.rating,
          review_count=excluded.review_count,
          location=excluded.location,
          description=excluded.description,
          contact_phone=excluded.contact_phone,
          contact_website=excluded.contact_website,
          data_json=excluded.data_json,
          scraped_at=excluded.scraped_at
      `);
            this.db.serialize(() => {
                this.db.run('BEGIN TRANSACTION');
                for (const agency of agencies) {
                    stmt.run([
                        agency.name,
                        agency.rating,
                        agency.reviewCount,
                        agency.location,
                        agency.description,
                        agency.contactPhone,
                        agency.contactWebsite,
                        agency.clutchProfileUrl,
                        JSON.stringify(agency.dataJson),
                        new Date().toISOString().split('T')[0] + 'T' + new Date().toISOString().split('T')[1].split('.')[0]
                    ], function (err) {
                        if (err) {
                            console.warn(`Failed to upsert row for ${agency.clutchProfileUrl}: ${err.message}`);
                        }
                        else {
                            count++;
                        }
                    });
                }
                this.db.run('COMMIT', (err) => {
                    stmt.finalize();
                    if (err) {
                        reject(err);
                    }
                    else {
                        resolve(count);
                    }
                });
            });
        });
    }
    async getStats() {
        return new Promise((resolve, reject) => {
            const stats = {
                total: 0,
                ratings: {},
                topLocations: []
            };
            this.db.serialize(() => {
                this.db.get('SELECT COUNT(*) AS n FROM agencies', (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    stats.total = row?.n || 0;
                    this.db.all(`
            SELECT CAST(rating AS INT) AS bucket, COUNT(*) AS n 
            FROM agencies 
            WHERE rating IS NOT NULL 
            GROUP BY bucket 
            ORDER BY bucket
          `, (err, rows) => {
                        if (err) {
                            reject(err);
                            return;
                        }
                        for (const row of rows) {
                            stats.ratings[row.bucket.toString()] = row.n;
                        }
                        this.db.all(`
              SELECT location, COUNT(*) AS n 
              FROM agencies 
              WHERE location IS NOT NULL AND TRIM(location) <> '' 
              GROUP BY location 
              ORDER BY n DESC 
              LIMIT 10
            `, (err, rows) => {
                            if (err) {
                                reject(err);
                                return;
                            }
                            stats.topLocations = rows.map(row => ({
                                location: row.location,
                                count: row.n
                            }));
                            resolve(stats);
                        });
                    });
                });
            });
        });
    }
    close() {
        this.db.close();
    }
}
exports.Database = Database;
//# sourceMappingURL=Database.js.map