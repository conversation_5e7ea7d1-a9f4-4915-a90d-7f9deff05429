!function(){"use strict";let e=null,t=null;function n(){let n=0,r=8;const o=document.querySelectorAll(".directory-only-related-block .provider-list-item");return e&&e.disconnect(),"#leaders-matrix"===window.location.hash||0===o.length?document.documentElement.style.setProperty("--ai-assistant-sidebar-trigger-display","flex"):(o.length<r&&(r=o.length),e=new IntersectionObserver((o=>{o.forEach((o=>{o.isIntersecting&&(n++,e?.unobserve(o.target),n>=r&&(document.documentElement.style.setProperty("--ai-assistant-sidebar-trigger-display","flex"),e?.disconnect(),t?.disconnect()))}))}),{threshold:.5,rootMargin:"0px 0px 0px 0px"}),void o.forEach((t=>e?.observe(t))))}!function(){const e=document.getElementById("providers__section");e&&(t=new MutationObserver(n),t?.observe(e,{childList:!0}))}(),n()}();