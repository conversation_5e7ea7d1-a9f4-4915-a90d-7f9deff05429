export interface Agency {
    name: string | null;
    rating: number | null;
    reviewCount: number | null;
    location: string | null;
    description: string | null;
    contactPhone: string | null;
    contactWebsite: string | null;
    clutchProfileUrl: string | null;
    dataJson: Record<string, any>;
}
export declare class AgencyBuilder {
    private agency;
    setName(name: string | null): this;
    setRating(rating: number | null): this;
    setReviewCount(reviewCount: number | null): this;
    setLocation(location: string | null): this;
    setDescription(description: string | null): this;
    setContactPhone(contactPhone: string | null): this;
    setContactWebsite(contactWebsite: string | null): this;
    setClutchProfileUrl(clutchProfileUrl: string | null): this;
    setDataJson(dataJson: Record<string, any>): this;
    build(): Agency;
}
//# sourceMappingURL=Agency.d.ts.map