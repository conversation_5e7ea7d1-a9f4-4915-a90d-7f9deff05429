!function(){"use strict";var t={8786:function(t,e,n){n.d(e,{J:function(){return r}});class r{constructor(t){t||console.error(`Event action is missing for ${this.action}, please add an action for object`),this.action=t,this.properties={}}setCategory(t){t?this.properties.event_category=t:console.error("Event category is required")}setLabel(t){t?this.properties.event_label=t:console.error("Event label is required")}setField(t,e){t&&null!=e?this.properties[t]=e:console.error(`.setField() method is failed. Please add name or values params: name - ${t}, value - ${e}`)}_collectGoogleAnalyticsKeys(){const t=[];return window.dataLayer?(window.dataLayer.forEach((e=>{"config"===e[0]&&e.length>=3&&!0===e[2].analyticjs&&!t.includes(e[1])&&t.push(e[1])})),t):t}sendGA(){const t=this.action,e=window.GlobalAnalyticsParameters?window.GlobalAnalyticsParameters.getParameters():{},n=this.properties;this._collectGoogleAnalyticsKeys().forEach((function(r){if(!r)return;const o={...e,...n};o.send_to=r;try{window.gtag("event",t,o)}catch(t){console.error(t)}}))}sendHeap(){if(window.heap)try{const t=this.action,e=window.GlobalAnalyticsParameters?window.GlobalAnalyticsParameters.getParameters():{},n=this.properties;window.heap.track(t,{...e,...n})}catch(t){console.error(t)}}send(){this.sendGA(),window.heap&&this.sendHeap()}}},7259:function(t,e,n){n.d(e,{XU:function(){return i},u5:function(){return a}});var r=n(8786);class o{constructor(t){this.parameters=t}setParameter(t){Object.assign(this.parameters,t)}getParameters(){return this.parameters}deleteParameter(t){delete this.parameters[t]}}function a(t,e,n){window.GlobalAnalyticsParameters=new o(n);const a=window.GlobalAnalyticsParameters?window.GlobalAnalyticsParameters.getParameters():{};window.gtag("config",t,{analyticjs:!0,debug_mode:!0,transport_url:e,...a}),window.addEventListener("CookiebotOnDecline",(function(){const t=!!window.navigator.globalPrivacyControl&&window.navigator.globalPrivacyControl,e=Cookiebot.consent.method;let n=new r.J("cookiebot_optout");n.setCategory("cookiebot"),n.setField("gpc_signal",t),n.setField("consent_method",e),n.send()}))}const i=function(){let t=!1;return function(){var e,n;t||(t=!0,e="a, button, label, select",n=["sg-accordion","sg-dropdown","sg-modal","sg-pagination","sg-shortlist","sg-carousel","sg-slider","sg-checkbox-v2","sg-reen-var2__card-action"],document.addEventListener("click",(function(t){let o;if(!(t.target.matches(e)||t.target.parentElement&&t.target.parentElement.matches(e)))return;if(o=t.target.matches(e)?t.target:t.target.parentElement,n.some((t=>[...o.classList].some((e=>e.includes(t))))))return;const a={...s(o)};let i=new r.J("autoclick");Object.keys(a).forEach((t=>{i.setField(t,a[t])})),i.send()})))}}();function s(t,e="dom_"){if(!t)return;let n=function(t,e="dom_"){if(!t)return;const n=Object.assign({},t.dataset),r={};return Object.keys(n).forEach((t=>{t.includes("gtm_")?r[t.replace("gtm_","")]=n[t]:r[`${e}_data-${t}`]=n[t]})),{...r,...t.classList.value&&{[`${e}_class_name`]:t.classList.value},...t.id&&{[`${e}_id`]:t.id},...t.parentElement&&t.parentElement.classList&&t.parentElement.classList.value&&{[`${e}_parent_class_name`]:t.parentElement.classList.value.trim()},...t.parentElement&&t.parentElement.id&&{[`${e}_parent_id`]:"string"==typeof t.parentElement.id?t.parentElement.id.trim():t.parentElement.id}}}(t,e);switch(t.localName){case"a":t.innerText?n[`${e}_label`]=t.innerText.trim():t.title?n[`${e}_label`]=t.title.trim():n[`${e}_label`]=t.getAttribute("aria-label");break;case"button":case"label":t.innerText?n[`${e}_label`]=t.innerText.trim():t.value?n[`${e}_label`]=t.value.trim():n[`${e}_label`]=t.getAttribute("aria-label");break;case"select":let r=t.id;n[`${e}_label`]=document.querySelector(`[for='${r}']`).innerText.trim()}return n}},2658:function(t,e,n){const r=t=>{const e=t+"=",n=decodeURIComponent(document.cookie).split(";");for(let t=0;t<n.length;t++){let r=n[t];for(;" "===r.charAt(0);)r=r.substring(1);if(0===r.indexOf(e))return r.substring(e.length,r.length)}return null},o=window.Analytics?window.Analytics:{},a=o.ga_id,i=o.heap_id,s=o.hubspot_id,c=o.bambora_id,u=o.js_date,l=o.transport_url,d=o.autoclicks,h=o.properties?o.properties:{},f=o.content_group,p=h.send_page_view,m=o.default_consent_enabled,g=o.block_scripts_for_consent,v=o.hotjar_enabled,y=o.hotjar_id,w=o.hotjar_limit,_=o.linkedIn_id,b=o.heap_updated,E=(t,e)=>{console.warn(`${t} parameter is required for AnalyticsJS configuration. Please check your window.Analytics object. Current values is: ${e}`)};let k=o.delay_hubspot_tracking;const j=new URLSearchParams(window.location.search),S=()=>{const t="hs_delay_off",e=new URLSearchParams(window.location.search).has(t),n=r("clutchsso"),o=null!==r(t);var a,i,s;return(e||n||o)&&(a=e,i=t,s=1,document.cookie=`${i}=${JSON.stringify(a)}; Max-Age=${6e3*s}; path=/`,k=0),k},P={disable_gtm_tracking:j.has("disable_gtm_tracking"),disable_heap_tracking:j.has("disable_heap_tracking"),disable_hubspot_tracking:j.has("disable_hubspot_tracking"),delay_hubspot_tracking:j.has("delay_hubspot_tracking"),...j.has("delay_hubspot_tracking")&&{delay_hubspot_tracking_timeout:1e3*j.get("delay_hubspot_tracking")}};var O=n(7259);h.content_group=f;const x=JSON.parse(localStorage.getItem("cookieConsentSettings"));function T(){if(!i||P.disable_heap_tracking)return void console.warn('Heap tracking is disabled - check heap_id parameter or remove "disable_heap_tracking" from the URL');window.heap=window.heap||[];const t=b?{initialize:function(t){window.heapReadyCb=window.heapReadyCb||[],heap.load=function(t,e){window.heap.envId=t,window.heap.clientConfig=e=e||{},window.heap.clientConfig.shouldFetchServerConfig=!1;var n=document.createElement("script");n.type="text/javascript",n.async=!0,n.src="https://cdn.us.heap-api.com/config/"+t+"/heap_config.js",n.dataset.cookieconsent="statistics";var r=document.getElementsByTagName("script")[0];r.parentNode.insertBefore(n,r);for(var o=["init","startTracking","stopTracking","track","resetIdentity","identify","identifyHashed","getSessionId","getUserId","getIdentity","addUserProperties","addEventProperties","removeEventProperty","clearEventProperties","addAccountProperties","addAdapter","addTransformer","addTransformerFn","onReady","addPageviewProperties","removePageviewProperty","clearPageviewProperties","trackPageview"],a=function(t){return function(){var e=Array.prototype.slice.call(arguments,0);window.heapReadyCb.push({name:t,fn:function(){heap[t]&&heap[t].apply(heap,e)}})}},i=0;i<o.length;i++)heap[o[i]]=a(o[i])},heap.load(t)}}:{initialize:function(t){heap.load=function(t,e){window.heap.appid=t,window.heap.config=e||{};const n=document.createElement("script");n.type="text/javascript",n.async=!0,n.src="https://cdn.heapanalytics.com/js/heap-"+t+".js",n.dataset.cookieconsent="statistics";const r=document.getElementsByTagName("script")[0];r.parentNode.insertBefore(n,r);for(let t=function(t){return function(){heap.push([t].concat(Array.prototype.slice.call(arguments,0)))}},e=["addEventProperties","addUserProperties","clearEventProperties","identify","resetIdentity","removeEventProperty","setEventProperties","track","unsetEventProperty"],n=0;n<e.length;n++)heap[e[n]]=t(e[n])},heap.load(t)}};t.initialize(i),function(){heap.clearEventProperties();const t=h;t.bot="",heap.addEventProperties(t),!1===p&&window.heap.track("page_load")}()}function C(){function t(){T(),function(){const t=sessionStorage.getItem("hj_loaded"),e=Math.random()<w;var n,r,o,a;v&&y&&(e||"true"===t)&&(n=window,r=document,n.hj=n.hj||function(){(n.hj.q=n.hj.q||[]).push(arguments)},n._hjSettings={hjid:y,hjsv:6},o=r.getElementsByTagName("head")[0],(a=r.createElement("script")).async=1,a.dataset.cookieconsent="statistics",a.src="https://static.hotjar.com/c/hotjar-"+n._hjSettings.hjid+".js?sv="+n._hjSettings.hjsv,o.appendChild(a),sessionStorage.setItem("hj_loaded","true"))}(),function(){if(_){let t=_;window._linkedin_data_partner_ids=window._linkedin_data_partner_ids||[],window._linkedin_data_partner_ids.push(t),function(t){if(!window.location.search.includes("disable_linkedin")){t||(window.lintrk=function(t,e){window.lintrk.q.push([t,e])},window.lintrk.q=[]);var e=document.getElementsByTagName("script")[0],n=document.createElement("script");n.type="text/javascript",n.async=!0,n.src="https://snap.licdn.com/li.lms-analytics/insight.min.js",e.parentNode.insertBefore(n,e)}}(window.lintrk)}}(),function(){if(c){const i=document.getElementById("user_id")?.content,s=document.getElementsByTagName("title")[0]?.innerHTML;t=window,e=document,n="script",t._ml=t._ml||{},t._ml.eid=c,t._ml.cid=s,t._ml.fp=i,r=e.getElementsByTagName(n)[0],o=new Date,(a=e.createElement(n)).async=1,a.src="https://ml314.com/tag.aspx?"+o.getDate()+o.getMonth(),r.parentNode.insertBefore(a,r)}var t,e,n,r,o,a}(),setTimeout((()=>{!function(){if(!s||P.disable_hubspot_tracking)return void console.warn('Hubspot tracking is disabled - check hubspot_id parameter or remove "disable_hubspot_tracking" from the URL');const t=document.createElement("script");t.type="text/javascript",t.async=!0,t.id="hs-script-loader",t.src=`https://js.hs-scripts.com/${s}.js`,t.dataset.cookieconsent="statistics";const e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(t,e);let n=window._hsq=window._hsq||[];n.push(["setPath",window.location.pathname]),n.push(["trackPageView"])}()}),S()||0)}x&&x?.statistics?t():g?window.addEventListener("CookiebotOnAccept",(function(){Cookiebot.consent.statistics&&t(),Cookiebot.consent.marketing&&gtag("consent","update",{ad_storage:"granted",ad_user_data:"granted",ad_personalization:"granted"})})):t()}!async function(){await(globalThis.scheduler?.yield?globalThis.scheduler.yield():new Promise((t=>{setTimeout(t,0)}))),function(){if(!f||!a||P.disable_gtm_tracking)return console.warn('GTM tracking is disabled - check content_group and ga_id parameters or remove "disable_gtm_tracking" from the URL'),!a&&E("ga_id",a),void(!f&&E("content_group",f));m&&(x&&x?.marketing?gtag("consent","default",{ad_personalization:"granted",ad_storage:"granted",ad_user_data:"granted",analytics_storage:"granted",functionality_storage:"granted",personalization_storage:"granted",security_storage:"granted",wait_for_update:500}):gtag("consent","default",{ad_personalization:"denied",ad_storage:"denied",ad_user_data:"denied",analytics_storage:"denied",functionality_storage:"denied",personalization_storage:"denied",security_storage:"granted",wait_for_update:500}),gtag("set","ads_data_redaction",!0),gtag("set","url_passthrough",!0));const t=document.createElement("script");t.type="text/javascript",t.async=!0,t.src=`${l||"https://www.googletagmanager.com"}/gtag/js?id=${a}`,t.dataset.cookieconsent="ignore";const e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(t,e),gtag("js",u),t.onload=function(){(0,O.u5)(a,l,h),x&&x?.marketing&&(gtag("consent","update",{ad_personalization:"granted",ad_storage:"granted",ad_user_data:"granted",analytics_storage:"granted",functionality_storage:"granted",personalization_storage:"granted",security_storage:"granted",wait_for_update:500}),gtag("cookie_consent_update"))},!1!==d&&(0,O.XU)(),!1!==p||gtag("event","page_view")}(),C()}();var A="_chatbotWidget",N="js";var I=function(t,e){if(!window.asset_links||!Object.keys(window.asset_links).length)return`static/${e}/${t}/${t}.${e}`;try{return"css"===e?window.asset_links[`${t}_scss`]:window.asset_links[`${t}_js`]}catch(t){return""}};var $=function(t,e){const n=document.getElementById(`static_${t}_${e}`);return Boolean(n)};var L,B=function t(e,n,r){if(!n)return Promise.all([t(e,N),t(e,"css")]).then((()=>{}));const o=I(e,n),a=r||`${window.location.origin}/${o}`;return new Promise(((t,r)=>{if($(e,n))return r(),void console.warn(`Async ${e}.${n} loading error: file is already loaded`);let o;n===N?(o=document.createElement("script"),o.setAttribute("nonce",document.querySelector("main")?.dataset.nonce||""),o.src=a):"css"===n&&(o=document.createElement("link"),o.rel="stylesheet",o.href=a),o.id=`static_${e}_${n}`,o.onload=()=>t(o),document.body.append(o)}))},U=(null===(L=document.getElementById("common-header"))||void 0===L?void 0:L.dataset.domain.replace(/(^\w+:|^)\/\//,""))||"clutch.co",M="https://"+U,G="/api/v1",q="".concat(G,"/shortlist/count"),J="".concat(G,"/messages/unread/count"),F="".concat(G,"/user/current"),R="https://account.".concat(U,"/sso.js");"https://bot.".concat(U,"/widget.js");new Set(["header_join","header_sign_in"]);const D=`https://vendor.${U}/onboarding/goal`;var H=function(){var t,e;document.getElementById("login_flow")||((e=document.createElement("script")).setAttribute("nonce",(null===(t=document.querySelector("main"))||void 0===t?void 0:t.dataset.nonce)||""),e.src=R,e.id="login_flow",e.onload=function(){document.querySelector("html").addEventListener("click",(function(t){if(t.target.classList.contains("sign-in-required")){t.preventDefault();var e=t.target,n=e.dataset.login_default,r=e.dataset.login_source,o=e.dataset.skip_user_role;window.login({isJoin:n,loginSource:r,skipUserRole:o,onSuccessLogin:function(t,e){!function(t,e){if(t.matches("a, button")){if(t.matches("a")){const n=t.getAttribute("target"),r=t.dataset.sign_in_url||t.getAttribute("data-sign-in-url"),o="/clutch-verified"===window.location.pathname;let a=e?.isNewUser&&r?r:t.getAttribute("href");o&&!1===e?.hasProvider&&(a=D),a&&(n?window.open(a):window.location.href=a)}window.header&&window.header.updateUserAuthStatus(!0)}}(t,null==e?void 0:e.originalEvent.detail)}},e)}}))},e.onerror=function(t){return new Error("Sso flow file loading error: ",t)},document.body.append(e))};const z=(t,e,n=5e3)=>new Promise(((r,o)=>{const a=Date.now(),i=()=>{t()?r():Date.now()-a>n?o(new Error(`Timeout waiting for ${e}`)):setTimeout(i,50)};i()}));var V=["POST","PUT","DELETE","PATCH"];var X=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t;if(V.includes(e.method)){var r=function(t){for(var e=t+"=",n=decodeURIComponent(document.cookie).split(";"),r=0;r<n.length;r++){for(var o=n[r];" "===o.charAt(0);)o=o.substring(1);if(0===o.indexOf(e))return o.substring(e.length,o.length)}return null}("__csrftoken");r&&(e.headers={"X-CSRF-Token":r})}return fetch(n,e)};function K(t){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},K(t)}function W(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Y(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?W(Object(n),!0).forEach((function(e){Q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):W(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Q(t,e,n){return(e=function(t){var e=function(t,e){if("object"!=K(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=K(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==K(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Z,tt="chat-counter",et=!1,nt=!0,rt=function(){return Math.floor((new Date).getTime()/1e3)},ot=function(){var t=st();ut(t)||(ct(),nt?lt().then(dt).then(ht).catch(ft):it())},at=function(){Z=setInterval(ot,arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e4)},it=function(){return new Promise((function(t){clearInterval(Z),t()}))},st=function(){var t=localStorage.getItem(tt);return t?JSON.parse(t):{}},ct=function(){localStorage.setItem(tt,JSON.stringify(Y(Y({},st()),{},{lastUpdate:rt()})))},ut=function(t){return rt()-Number(t&&t.lastUpdate)<10},lt=function(){return X(M+J,{method:"GET"})},dt=function(t){if(401===t.status)return it().then((function(){nt=!1})),{};if(!t.ok)throw new Error("Failed with status ".concat(t.status));return t.json()},ht=function(t){t.data.count&&(localStorage.setItem(tt,JSON.stringify({count:t.data.count,lastUpdate:rt()})),pt(t.data.count),et&&nt&&it().then((function(){at(1e4)})))},ft=function(t){localStorage.setItem(tt,JSON.stringify({lastUpdate:rt()})),nt&&(et=!0,it().then((function(){at(6e4),console.error("Error chat updating: ",t)})))},pt=function(t){return Et.updateMessagesCountElementText(t)},mt=function(t){var e=document.getElementById("shortlist-count"),n=document.getElementById("mobile-shortlist-count");e&&(e.textContent=t,e.setAttribute("data-count",t)),n&&(n.textContent=t)},gt=function(){return vt().then((function(t){return mt(t),t})).catch((function(t){return t}))},vt=function(){return X(M+q,{method:"GET",credentials:"include",headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}}).then((function(t){if(t.ok)return t.text();throw new Error(t.status)})).then((function(t){var e=JSON.parse(t);return Number(e.data.count)})).catch((function(t){throw new Error(t)}))},yt=function(t){var e=document.getElementById("message-count"),n=document.getElementById("mobile-message-count");e&&(e.textContent=t,e.setAttribute("data-count",t)),n&&(n.textContent=t)},wt=function(){return X(M+J,{method:"GET",credentials:"include",headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}}).then((function(t){if(t.ok)return t.text();throw new Error(t.status)})).then((function(t){var e=JSON.parse(t);return Number(e.data.count)})).catch((function(t){throw new Error(t)}))},_t=function(){return X(M+F,{method:"GET",credentials:"include",headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}}).then((function(t){if(t.ok)return t.text();throw new Error(t.status)})).then((function(t){return JSON.parse(t).data})).catch((function(t){throw new Error(t)}))},bt=function(){var t=document.getElementById("user-menu"),e=document.getElementById("mobile-menu-user-item"),n=document.getElementById("common-header"),r=document.querySelector("main");if(t){var o=document.createElement("div");for(o.innerHTML='\n        <button\n            id="sign-in-link"\n            class="header__sign-in-link header__sign-in-button sign-in header__secondary-link sign-in-required"\n            type="button"\n            aria-label="Sign in"\n            data-login_source="header_sign_in"\n        >\n            Sign In\n        </button>\n        <button\n            class="modal-sso__open-modal sg-modal--sso-join sign-in-required"\n            aria-label="Join"\n            data-login_default="join"\n            type="button"\n            id="show-sso-modal"\n            data-login_source="header_join"\n        >\n            Join\n        </button>';o.firstChild;)t.parentNode.insertBefore(o.firstChild,t);t.remove()}if(e){var a=document.createElement("div");a.innerHTML='\n            <ul class="sign-in-list">\n                <li>\n                    <button \n                        id="show-sso-modal"\n                        type="button"\n                        class="show-sso-modal sign-in sign-in-required"\n                        aria-label="Join"\n                        data-login_default="join"\n                        data-login_source="header_join"\n                    >\n                        Join\n                    </button>\n                </li>\n                <li id="mobile-sign-in">\n                    <button\n                        class="sign-in sign-in--login sign-in-required"\n                        type="button"\n                        aria-label="Sign in"\n                        data-login_source="header_sign_in"\n                    >\n                        Sign In\n                    </button>\n                </li>\n            </ul>',e.parentNode.replaceChild(a.firstElementChild,e)}n&&n.setAttribute("data-is-authed","false"),r&&r.setAttribute("data-is-authed","false")},Et={updateShortlistCount:gt,fetchShortlistCount:vt,updateShortlistCountElementText:mt,updateMessagesCount:function(){return wt().then((function(t){return yt(t),t})).catch((function(t){return t}))},fetchMessagesCount:wt,updateMessagesCountElementText:yt,updateUserAuthStatus:function(t){t?_t().then((function(t){var e=document.getElementById("common-header"),n=document.getElementById("sign-in-link"),r=document.querySelector(".sign-in-list"),o=document.querySelector("main"),a=document.getElementById("show-sso-modal");if(e){var i=e.dataset.domain||"";if(n){var s='<div id="user-menu" class="header__user-menu">\n                    <button\n                        type="button"\n                        aria-label="Open User Menu"\n                        data-url="'.concat(i,"/user/menu?next=").concat(window.location.pathname+window.location.search,'"\n                        class="header__user-menu-button header__secondary-link"\n                    >\n                        <span class="header__user-menu-avatar">\n                            ').concat((null==t?void 0:t.avatar)&&'<img alt="Me" src="'.concat(null==t?void 0:t.avatar,'">'),'\n                        </span>\n                        Me\n                    </button>\n\n                    <div\n                        id="user-menu-container"\n                        class="header__user-menu-list"\n                    ></div>\n                </div>'),c=document.createElement("div");c.innerHTML=s,n.parentNode.replaceChild(c.firstElementChild,n)}var u=null!=t&&t.avatar?'<img alt="Me" src="'.concat(t.avatar,'" />'):"";if(r){var l='<li id="mobile-menu-user-item">\n                    <button\n                        type="button"\n                        aria-label="Open User Menu"\n                        class="header__user-menu-button header__mobile-menu-list-button"\n                        data-for="#mobile-user-menu"\n                    >\n                        <span class="header__user-menu-avatar">\n                            '.concat(u,"\n                        </span>\n                        Me\n                    </button>\n                </li>"),d=document.createElement("div");d.innerHTML=l,r.parentNode.replaceChild(d.firstElementChild,r)}e.setAttribute("data-is-authed","true"),o&&o.setAttribute("data-is-authed","true"),a&&a.remove()}})).catch((function(t){bt(),console.error("updateUserStatusToAuthed error: ",t)})).finally((function(){gt()})):bt()},fetchCurrentUser:_t,runMessageCounter:function(){"visible"===document.visibilityState&&at(),window.addEventListener("storage",(function(t){if(t.key===tt){var e=JSON.parse(t.newValue);e&&e.count&&pt(e.count)}})),document.addEventListener("visibilitychange",(function(){"visible"===document.visibilityState?(nt=!0,at()):it().then((function(){nt=!1}))}))}};function kt(t){return kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kt(t)}function jt(){jt=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(e,n,r,o){var a=n&&n.prototype instanceof h?n:h,i=Object.create(a.prototype);return c(i,"_invoke",function(e,n,r){var o=1;return function(a,i){if(3===o)throw Error("Generator is already running");if(4===o){if("throw"===a)throw i;return{value:t,done:!0}}for(r.method=a,r.arg=i;;){var s=r.delegate;if(s){var c=b(s,r);if(c){if(c===d)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===o)throw o=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=3;var u=l(e,n,r);if("normal"===u.type){if(o=r.done?4:2,u.arg===d)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=4,r.method="throw",r.arg=u.arg)}}}(e,r,new j(o||[])),!0),i}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var d={};function h(){}function f(){}function p(){}var m={};c(m,a,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(S([])));v&&v!==n&&r.call(v,a)&&(m=v);var y=p.prototype=h.prototype=Object.create(m);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){function n(o,a,i,s){var c=l(t[o],t,a);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==kt(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){n("next",t,i,s)}),(function(t){n("throw",t,i,s)})):e.resolve(d).then((function(t){u.value=t,i(u)}),(function(t){return n("throw",t,i,s)}))}s(c.arg)}var o;c(this,"_invoke",(function(t,r){function a(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(a,a):a()}),!0)}function b(e,n){var r=n.method,o=e.i[r];if(o===t)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=t,b(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var a=l(o,e.i,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,d;var i=a.arg;return i?i.done?(n[e.r]=i.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,d):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,d)}function E(t){this.tryEntries.push(t)}function k(e){var n=e[4]||{};n.type="normal",n.arg=t,e[4]=n}function j(t){this.tryEntries=[[-1]],t.forEach(E,this),this.reset(!0)}function S(e){if(null!=e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(kt(e)+" is not iterable")}return f.prototype=p,c(y,"constructor",p),c(p,"constructor",f),f.displayName=c(p,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,s,"GeneratorFunction")),t.prototype=Object.create(y),t},e.awrap=function(t){return{__await:t}},w(_.prototype),c(_.prototype,i,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new _(u(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},w(y),c(y,s,"Generator"),c(y,a,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.unshift(r);return function t(){for(;n.length;)if((r=n.pop())in e)return t.value=r,t.done=!1,t;return t.done=!0,t}},e.values=S,j.prototype={constructor:j,reset:function(e){if(this.prev=this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(t){i.type="throw",i.arg=e,n.next=t}for(var o=n.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a[4],s=this.prev,c=a[1],u=a[2];if(-1===a[0])return r("end"),!1;if(!c&&!u)throw Error("try statement without catch or finally");if(null!=a[0]&&a[0]<=s){if(s<c)return this.method="next",this.arg=t,r(c),!0;if(s<u)return r(u),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r[0]>-1&&r[0]<=this.prev&&this.prev<r[2]){var o=r;break}}o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]&&(o=null);var a=o?o[4]:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o[2],d):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),k(n),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[0]===t){var r=n[4];if("throw"===r.type){var o=r.arg;k(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={i:S(e),r:n,n:r},"next"===this.method&&(this.arg=t),d}},e}function St(t,e,n,r,o,a,i){try{var s=t[a](i),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,o)}function Pt(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(t){St(a,r,o,i,s,"next",t)}function s(t){St(a,r,o,i,s,"throw",t)}i(void 0)}))}}var Ot,xt=["click","mousemove","mousedown","keydown","touchstart","touchmove","scroll"];Ot=Pt(jt().mark((function t(){return jt().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,globalThis.scheduler?.yield?globalThis.scheduler.yield():new Promise((t=>{setTimeout(t,0)}));case 2:H(),Ct(),At(),Nt();case 6:case"end":return t.stop()}}),t)}))),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",Ot):Ot();var Tt=function(){return null!==document.getElementById("static__header_js")};function Ct(){Object.values({search:{selectors:".search_mobile__button, #menu_search--button",readyFlag:"liveSearchReady",event:"OPEN_LIVE_SEARCH",files:[{name:"_header"},{name:"searchTracking",type:"js"},{name:"_liveSearch",type:"js"}]},menu:{selectors:"#mobile-menu-button",readyFlag:"mobileMenuReady",event:"OPEN_MOBILE_MENU",files:[{name:"_header"}]}}).forEach((function(t){document.querySelectorAll(t.selectors).forEach((function(e){var n=function(){var e=Pt(jt().mark((function e(){var n;return jt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Tt()){e.next=2;break}return e.abrupt("return");case 2:if(e.prev=2,!window[t.readyFlag]){e.next=5;break}return e.abrupt("return");case 5:n=t.files.map((function(t){return B(t.name,t.type||null)})),Promise.all(n).then((function(){z((function(){return window[t.readyFlag]}),"Header scripts initialization for ".concat(t.event)).then((function(){window.header=Et,document.dispatchEvent(new Event(t.event))}))})).catch((function(t){return t})),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),console.error("Error loading header functionality:",e.t0);case 12:case"end":return e.stop()}}),e,null,[[2,9]])})));return function(){return e.apply(this,arguments)}}();e.addEventListener("click",n,{once:!0})}))}));var t=function(){Tt()||(B("_header").then((function(){window.header=Et,B("searchTracking","js").catch((function(t){return t})),B("_liveSearch","js").catch((function(t){return t}))})).catch((function(t){return t})),xt.forEach((function(e){document.removeEventListener(e,t,{once:!0,capture:!0})})))};xt.forEach((function(e){document.addEventListener(e,t,{once:!0,capture:!0})}))}function At(){var t=new IntersectionObserver((function(e){e.forEach((function(e){e.isIntersecting&&(B("_footer","js").catch((function(t){return t})),t.disconnect())}))})),e=document.querySelector("footer, .programmatic-content");e&&t.observe(e)}function Nt(){var t;"true"===sessionStorage.getItem("ai-assistant-should-open")?(t=!0,window.innerWidth<=767||document.getElementsByTagName("body")[0].setAttribute("data-layout-split",String(t)),B(A,N)):function(t){const e=["mousemove","mousedown","keydown","touchstart","wheel"];let n=!1;const r=()=>{n||(n=!0,e.forEach((t=>{document.removeEventListener(t,r,!0),window.removeEventListener(t,r,!0)})),t())};e.forEach((t=>{document.addEventListener(t,r,{capture:!0}),window.addEventListener(t,r,{capture:!0})}))}((function(){return B(A,N)}))}},1679:function(t,e,n){n.p}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var a=e[r]={exports:{}};return t[r](a,a.exports,n),a.exports}n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(1679),n(2658);n(7259)}();