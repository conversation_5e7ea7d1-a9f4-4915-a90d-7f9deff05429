"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HtmlParser = void 0;
const cheerio = __importStar(require("cheerio"));
const fs_1 = require("fs");
const Agency_1 = require("../models/Agency");
class HtmlParser {
    async parseFile(filePath, options = {}) {
        try {
            const htmlContent = await fs_1.promises.readFile(filePath, 'utf-8');
            return this.parseHtml(htmlContent, options);
        }
        catch (error) {
            throw new Error(`Failed to read file ${filePath}: ${error.message}`);
        }
    }
    parseHtml(htmlContent, options = {}) {
        const $ = cheerio.load(htmlContent);
        const agencies = [];
        const listings = $('li.provider-row, div.provider-row, li.directory-listing, div.directory-listing');
        let elementsToProcess = listings;
        if (listings.length === 0) {
            const profileLinks = $('a[href^="/profile/"]');
            const parents = profileLinks.map((_, el) => {
                const parent = $(el).closest('li, div');
                return parent.length > 0 ? parent[0] : null;
            }).get().filter(Boolean);
            elementsToProcess = $(parents);
        }
        elementsToProcess.each((_, card) => {
            try {
                const agency = this.parseCard($(card));
                if (options.minRating !== undefined &&
                    (agency.rating === null || agency.rating < options.minRating)) {
                    return;
                }
                agencies.push(agency);
            }
            catch (error) {
                console.debug(`Failed to parse a listing card: ${error.message}`);
            }
        });
        return agencies;
    }
    parseCard($card) {
        const builder = new Agency_1.AgencyBuilder();
        const nameEl = $card.find('a[href^="/profile/"]').first();
        if (nameEl.length === 0) {
            const fallbackNameEl = $card.find('h3 a, h2 a').first();
            if (fallbackNameEl.length > 0) {
                builder.setName(fallbackNameEl.text().trim() || null);
                const href = fallbackNameEl.attr('href');
                if (href) {
                    const profileUrl = href.startsWith('/') ? `https://clutch.co${href}` : href;
                    builder.setClutchProfileUrl(profileUrl);
                }
            }
        }
        else {
            builder.setName(nameEl.text().trim() || null);
            const href = nameEl.attr('href');
            if (href) {
                const profileUrl = href.startsWith('/') ? `https://clutch.co${href}` : href;
                builder.setClutchProfileUrl(profileUrl);
            }
        }
        builder.setRating(this.extractRating($card));
        builder.setReviewCount(this.extractReviewCount($card));
        builder.setLocation(this.extractLocation($card));
        builder.setDescription(this.extractDescription($card));
        builder.setContactPhone(this.extractPhone($card));
        builder.setContactWebsite(this.extractWebsite($card));
        const classes = $card.attr('class');
        builder.setDataJson({
            rawHtmlClass: classes ? classes.split(' ') : []
        });
        return builder.build();
    }
    extractRating($card) {
        const selectors = [
            'span.rating, span.clutch-average, span[itemprop="ratingValue"]',
            'div.rating span',
            '[class*="rating"]'
        ];
        for (const selector of selectors) {
            const el = $card.find(selector).first();
            if (el.length > 0) {
                const value = this.toFloat(el.text());
                if (value !== null) {
                    return value;
                }
            }
        }
        const text = $card.text();
        const ratingMatch = text.match(/(\d+(?:\.\d)?)\s*\/\s*5/);
        if (ratingMatch) {
            return this.toFloat(ratingMatch[1]);
        }
        const standaloneMatch = text.toLowerCase().match(/(\d+\.\d)(?=\s*(?:rating|stars))/);
        if (standaloneMatch) {
            return this.toFloat(standaloneMatch[1]);
        }
        return null;
    }
    extractReviewCount($card) {
        const text = $card.text();
        const match = text.match(/(\d{1,5})\+?\s+review/i);
        return match ? parseInt(match[1], 10) : null;
    }
    extractLocation($card) {
        const selectors = [
            'span.locality, span.location, .list-item__address, .location',
            '[class*="location"]'
        ];
        for (const selector of selectors) {
            const el = $card.find(selector).first();
            if (el.length > 0) {
                const text = el.text().trim();
                if (text) {
                    return text;
                }
            }
        }
        return null;
    }
    extractDescription($card) {
        const selectors = ['.module-list p', '.description', '.summary', 'p'];
        for (const selector of selectors) {
            const el = $card.find(selector).first();
            if (el.length > 0) {
                const text = el.text().trim();
                if (text && text.split(' ').length >= 3) {
                    return text;
                }
            }
        }
        return null;
    }
    extractPhone($card) {
        const text = $card.text();
        const match = text.match(/(\+?\d[\d\s().-]{6,}\d)/);
        return match ? match[1] : null;
    }
    extractWebsite($card) {
        const links = $card.find('a');
        for (let i = 0; i < links.length; i++) {
            const link = links.eq(i);
            const href = link.attr('href');
            const label = link.text().toLowerCase();
            if (href && href.startsWith('http') &&
                (label.includes('website') || label.includes('visit'))) {
                return href;
            }
        }
        return null;
    }
    toFloat(s) {
        if (!s) {
            return null;
        }
        try {
            const num = parseFloat(s.trim());
            return isNaN(num) ? null : num;
        }
        catch {
            return null;
        }
    }
}
exports.HtmlParser = HtmlParser;
//# sourceMappingURL=HtmlParser.js.map