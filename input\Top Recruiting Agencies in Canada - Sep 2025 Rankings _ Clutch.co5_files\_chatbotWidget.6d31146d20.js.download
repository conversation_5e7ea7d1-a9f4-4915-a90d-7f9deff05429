!function(){"use strict";var t,e=(null===(t=document.getElementById("common-header"))||void 0===t?void 0:t.dataset.domain.replace(/(^\w+:|^)\/\//,""))||"clutch.co",n="/api/v1",o=("".concat(n,"/shortlist/count"),"".concat(n,"/messages/unread/count"),"".concat(n,"/user/current"),"https://account.".concat(e,"/sso.js"),"https://bot.".concat(e,"/widget.js"));!function(){const t=document.querySelector("header");if(!t||"true"!==t.dataset.chatbotWidgetEnabled)return;if(document.getElementById("chatbot-widget-script"))return;const e=document.createElement("script");e.setAttribute("nonce",t?.dataset.nonce||""),e.src=o,e.id="chatbot-widget-script",e.onload=()=>{window.createAiAssistant?.()},e.onerror=t=>{console.error("Chatbot widget script file loading error: ",t)},document.body.append(e)}(),document.addEventListener("TOGGLE_ASSISTANT_SIDEBAR",(function(t){var e;e=t.detail.open,window.innerWidth<=767||document.getElementsByTagName("body")[0].setAttribute("data-layout-split",String(e))}))}();