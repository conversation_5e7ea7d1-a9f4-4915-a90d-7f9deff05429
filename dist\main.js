#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = main;
const cli_1 = require("./cli/cli");
async function main() {
    const cli = new cli_1.ClutchScrapeCli();
    try {
        await cli.run(process.argv);
    }
    catch (error) {
        console.error('Application error:', error.message);
        process.exit(1);
    }
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=main.js.map